# Activity 1 - (5%)
## Due Date - Sunday September 21, 2025 (Late Policy: 10% per day)
## Questions


1. Spam Filter Email Agent (50%)

Create an agent which filters email classifying it as either spam or not.

Email is typically stored in [.eml format](https://docs.fileformat.com/email/eml/#brief-history-of-eml) composed of headers and a body.  The file is stored in text format with the header in plain text.  The body contents can be encrypted or encoded in arbitrary formats .  In this case we assume attachments of type “text” are readable by the agent

The system has access to 
-	an _allow list_, which lists safe domains…emails coming from that domain are classified as non-spam regardless of its contents 
-	an _restrict list_, which lists safe domains…emails coming from that domain are classified as spam regardless of its contents 
-	a _bad word list_, which list words that are likely to be in spam emails. If more than 5 words within the body of the email are found on this list and it is not coming from a domain on the allow list, then it should be considered spam 

Spam email should be placed in a spam directory.  Others should be a placed in an email directory 


a) Specify the *task environment* for this problem using PEAS.
    Performance -> Filtering the emails recieved into spam if it goes there 
    Environment -> The email inbox, spam folder, allow list, restrict list, bad word list
    Actuator ->  Moving the emails
    Sensor -> An email entering the inbox

b)	List the properties of the task environment.

    Fully Observable (Can see all the emails and its contents)
    Single Agent (Only one agent filtering the emails)
    Deterministic (The emails are either spam or not spam)
    Episodic (The emails come in one at a time)     
    Dynamic (While the emails are not changing, the enviorment can change while the agent is working, ex. new emails coming in)
    Known (The rules for filtering the emails are known)


c)	What type of agent is most appropriate for this problem?  Justify your answer.

    Agent Type: Simple Reflex Agent -> This agent is appropriate for the problem because the enviorment is all fully obsrvable and the agent does not need to know the history of the emails to determine if it is spam or not.


d)	Implement an agent that acts rationally in this environment.  Your implementation must follow the structure/form of the agent type selected from the text.

    Found in Q1.cpp


 
2. Search Problem (50%)

You have three jugs, measuring 12 gallons, 8 gallons, and 3 gallons, and a water faucet. You can fill the jugs up or empty them out from one to another or onto the ground. You need to measure out exactly one gallon.

a) Define your _states_ (initial, goal(s)), _action(s)_, _transition model_

b) Does a heuristic function makes sense for this problem?  If so, informally define what it would be. (A description as oppose to a precise mathematical function is sufficent.) 

c) Is _any_ search algorithm guarenteed to reach a goal state?  Explain your answer.

d)	Implement the breadth first search algorithm as described in Figure 3.9 in order to build a tree for your defined state space and find a path from the initial to goal states.


 ## Submission Notes

 - 30% of the grade will be the presentation of your work
 - Presentations will take place on **Tuesday September 30** during lab time
 - Implementations must include runnable tests along with test data.  Instructions for running these tests must be provided.  Preferably GitActions are used to have the tests run automatically.
 - Implementations can be done in either Python or C++ but must use the pseudocode snippets provided in the text to guide your design.
 
