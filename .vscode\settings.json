{"files.associations": {"*.embeddedhtml": "html", "iostream": "cpp", "algorithm": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "fstream": "cpp", "initializer_list": "cpp", "ios": "cpp", "iosfwd": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "memory": "cpp", "new": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "utility": "cpp", "vector": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocinfo": "cpp", "xlocnum": "cpp", "xmemory": "cpp", "xstddef": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xutility": "cpp"}}